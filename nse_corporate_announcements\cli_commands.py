"""
Command execution logic for NSE Corporate Announcements Scraper CLI
Handles the actual data fetching and processing operations using shared CLI framework.
"""

import sys
import os
from typing import Optional, Dict, Any

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.cli_framework import BaseCommandExecutor, DataDisplayHelper
from shared.data_processor import FileManager
from .scraper import NSECorporateAnnouncementsScraper


class NSECorporateAnnouncementsCommandExecutor(BaseCommandExecutor):
    """NSE Corporate Announcements specific command executor using shared framework"""

    def __init__(self):
        super().__init__("NSE Corporate Announcements")
        self.file_manager = FileManager("nse_corporate_announcements_data", "NSE_CORP_ANN")

    def _create_scraper(self, args=None):
        """Create and return NSE Corporate Announcements scraper instance"""
        return NSECorporateAnnouncementsScraper()

    def _handle_list_files_command(self, args):
        """Handle the --list-files command"""
        files = self.file_manager.list_saved_files()
        DataDisplayHelper.display_file_list(files, "NSE Corporate Announcements")

    def _perform_scraping(self, scraper, args) -> Optional[Dict[str, Any]]:
        """Perform the actual scraping based on arguments"""
        print(f"\n🔄 Fetching corporate announcements...")
        index = getattr(args, 'index', 'equities')
        df = scraper.fetch_corporate_announcements(index=index)
        return {
            'dataframe': df,
            'data_type': 'corporate_announcements',
            'start_date': 'N/A',
            'end_date': 'N/A'
        } if df is not None else None

    def _display_results(self, scraper, result: Dict[str, Any], args) -> bool:
        """Display scraping results and handle data saving"""
        dataframe = result['dataframe']
        data_type = result['data_type']

        print(f"\n✅ Data fetched successfully!")
        print(f"Total records: {len(dataframe)}")

        if len(dataframe) > 0:
            # Display summary using shared helper
            DataDisplayHelper.display_summary(dataframe, "NSE", "corporate announcements")

            # Save data unless summary-only is specified
            if not args.summary_only:
                self._save_data(scraper, dataframe, args.output)
            else:
                print("\n📋 Summary-only mode: Data not saved")
        else:
            print("📊 No data found for the specified criteria.")

        return True

    def _save_data(self, scraper, dataframe, custom_filename: str = None):
        """Save data to CSV and database"""
        # Save to CSV
        csv_success = scraper.save_to_csv(dataframe, custom_filename)
        if not csv_success:
            print("⚠️ Warning: Failed to save data to CSV file")

        # Save to database
        db_result = scraper.save_to_database(dataframe)
        if not db_result['success']:
            print("⚠️ Warning: Database save had errors")


def execute_command(args):
    """Execute the main command based on parsed arguments"""
    # Use the new command executor
    executor = NSECorporateAnnouncementsCommandExecutor()
    executor.execute_command(args)
