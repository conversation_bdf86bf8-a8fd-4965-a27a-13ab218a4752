"""
Configuration file for NSE Corporate Announcements Scraper
Contains essential constants and configuration settings.
"""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_config import NSEConfig

# Create a custom configuration class for corporate announcements
class NSECorporateAnnouncementsConfig(NSEConfig):
    """NSE Corporate Announcements specific configuration"""

    def __init__(self):
        super().__init__()
        # Override specific settings for corporate announcements
        self._output_folder = "nse_corporate_announcements_data"
        self._database_table = "nse_corporate_announcements"
        # Disable proxy for corporate announcements
        self.USE_PROXY = False

    @property
    def OUTPUT_FOLDER(self):
        """Get the output folder for corporate announcements"""
        return self._output_folder

    @property
    def DATABASE_TABLE(self):
        """Get the database table name for corporate announcements"""
        return self._database_table

    def get_api_endpoint(self):
        """Get the corporate announcements API endpoint"""
        return f"{self.BASE_URL}/api/corporate-announcements"

# Create the configuration instance
config = NSECorporateAnnouncementsConfig()

# Export commonly used settings for backward compatibility
NSE_BASE_URL = config.BASE_URL
REQUEST_TIMEOUT = config.REQUEST_TIMEOUT
OUTPUT_FOLDER = config.OUTPUT_FOLDER
# Override Supabase URL for the new project
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"
SAVE_CSV = config.SAVE_CSV
USE_DATABASE = config.USE_DATABASE
DATABASE_TABLE = config.DATABASE_TABLE
CSV_FILENAME = "nse_corporate_announcements_persistent.csv"

# NSE-specific settings
from shared.utils import get_api_headers
NSE_HEADERS = get_api_headers("NSE Corporate Announcements", config.BASE_URL)

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
